package com.swhd.agent.api.account.dto.param.record;

import com.swj.magiccube.api.PageReq;
import java.math.BigDecimal;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 * @since 2025-05-26
 */
@Getter
@Setter
@Accessors(chain = true)
@Schema(description = "AccountOceanengineCustomerCreditChangeRecordPageParam对象")
public class AccountOceanengineCustomerCreditChangeRecordPageParam extends PageReq {

    @Schema(description = "授信ID")
    private Long selfRechargeCreditId;

    @Schema(description = "变更类型 CLOSE-关闭 OPEN-开启 INCREASE-提额 CONSUME-客户消耗 CONSUME_ROLLBACK-客户消耗回滚")
    private String changeType;

    @Schema(description = "变更前额度")
    private BigDecimal beforeAmount;

    @Schema(description = "变更后额度")
    private BigDecimal afterAmount;

    @Schema(description = "变更额度")
    private BigDecimal changeAmount;

    @Schema(description = "充值明细ID")
    private Long rechargeDetailId;

}
